<template>
  <el-dialog
    :visible="visible"
    width="900px"
    :close-on-click-modal="false"
    center
    custom-class="create-agent-dialog"
    :z-index="2000"
    :top="'5vh'"
    @close="handleClose"
  >
    <div class="create-agent-dialog-content">
      <div class="dialog-header">
        <span class="dialog-title">选择创建智能体</span>
        <img
          src="@/assets/app/close-icon.png"
          alt="关闭"
          class="close-icon"
          @click="handleClose"
        />
      </div>

      <div class="agent-type-selection">
        <div class="agent-type-card light" @click="selectAgentType('light')" :class="{ active: selectedAgentType === 'light' }">
          <div class="agent-type-header">
            <img src="@/assets/app/light-agent-icon.png" alt="轻量智能体" class="agent-type-icon" />
            <span class="agent-type-name">轻量智能体</span>
          </div>
          <span class="agent-type-desc">适用于简单对话和基础任务处理</span>
        </div>
        <div class="agent-type-card knowledge" @click="selectAgentType('knowledge')" :class="{ active: selectedAgentType === 'knowledge' }">
          <div class="agent-type-header">
            <img src="@/assets/app/knowledge-agent-icon.png" alt="知识智能体" class="agent-type-icon" />
            <span class="agent-type-name">知识智能体</span>
          </div>
          <span class="agent-type-desc">适用于查询知识库与数据库能力</span>
        </div>
      </div>

      <div class="form-section">
        <div class="form-item">
          <div class="form-label">
            <span>智能体头像</span>
            <span class="required">*</span>
          </div>
          <div class="avatar-upload">
            <div class="avatar-preview" @click="triggerAvatarUpload">
              <img v-if="agentForm.avatar" :src="agentForm.avatar" alt="头像" class="avatar-img" />
              <div v-else class="avatar-placeholder">
                <i class="el-icon-plus"></i>
              </div>
              <input
                ref="avatarInput"
                type="file"
                accept="image/jpeg,image/png"
                style="display: none;"
                @change="handleAvatarUpload"
              />
            </div>
            <span class="upload-tip">智能体头像支持 jpg、png 格式，大小不超过 2MB</span>
          </div>
        </div>

        <div class="form-item">
          <div class="form-label">
            <span>智能体名称</span>
            <span class="required">*</span>
          </div>
          <el-input
            v-model="agentForm.name"
            placeholder="请输入智能体名称"
            class="form-input"
          />
        </div>

        <div class="form-item">
          <div class="form-label">
            <span>智能体描述</span>
            <span class="required">*</span>
          </div>
          <div class="description-header">
            <span @click="aiFillDescription">AI一键填写</span>
            <span @click="goToTemplateCenter">去模版中心复制</span>
          </div>
          <el-input
            v-model="agentForm.description"
            type="textarea"
            :rows="3"
            placeholder="智能体是做什么的？简单描述它吧～"
            class="form-textarea"
            maxlength="100"
            show-word-limit
          />
          <div class="expand-section">
            <img src="@/assets/app/expand-icon.png" alt="展开" class="expand-icon" />
          </div>
        </div>

        <div class="form-item">
          <div class="form-label">
            <span>开场介绍</span>
            <span class="required">*</span>
          </div>
          <div class="intro-tip">
            将在用户开启对话时展示，引导用户快速了解功能并开启对话。例如："需要什么帮助？"
          </div>
          <el-input
            v-model="agentForm.introduction"
            type="textarea"
            :rows="4"
            placeholder="请输入开场介绍"
            class="form-textarea"
            maxlength="1000"
            show-word-limit
          />
          <div class="expand-section">
            <img src="@/assets/app/expand-icon.png" alt="展开" class="expand-icon" />
          </div>
        </div>

        <div class="form-item">
          <div class="form-label">
            <span>智能体分类</span>
            <span class="required">*</span>
          </div>
          <el-select
            v-model="agentForm.applicationTypeCode"
            placeholder="请选择智能体分类"
            class="form-select"
            @change="handleCategoryChange"
          >
            <el-option
              v-for="item in categories"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            ></el-option>
          </el-select>
        </div>

        <div class="form-item">
          <div class="form-label">
            <span>智能体设定</span>
          </div>
          <div class="setting-tip">输入提示词，定义智能体行为</div>
          <el-input
            v-model="agentForm.setting"
            type="textarea"
            :rows="6"
            placeholder="请输入智能体设定"
            class="form-textarea"
            maxlength="20000"
            show-word-limit
          />
          <div class="expand-section">
            <img src="@/assets/app/expand-icon.png" alt="展开" class="expand-icon" />
          </div>
        </div>

        <div class="form-item">
          <div class="form-label">
            <span>是否公开</span>
          </div>
          <div class="public-switch">
            <el-switch v-model="agentForm.isPublic" />
            <span class="public-tip">(如选择公开则可在应用广场被搜索和使用)</span>
          </div>
        </div>

        <div class="form-item" v-if="selectedAgentType === 'knowledge' && !showUploadSection">
          <div class="form-label">
            <span>知识库绑定</span>
          </div>
          <div class="knowledge-base-selection">
            <div class="selected-knowledge">
              <span v-if="selectedKnowledgeIds.length === 0">请选择知识库</span>
              <div v-else class="knowledge-tags">
                <el-tag
                  v-for="kb in selectedKnowledgeList"
                  :key="kb.id"
                  closable
                  @close="removeKnowledgeSelection(kb.id)"
                  class="knowledge-tag"
                >
                  {{ kb.name }}
                </el-tag>
              </div>
              </div>
            <el-button type="primary" @click="showKnowledgeDialog">选择知识库</el-button>
            </div>
          <div class="form-tip">选择需要绑定的知识库，支持多个知识库</div>
          <div class="direct-upload">
            <el-button type="text" class="direct-upload-btn" @click="toggleUploadSection">
              <i class="el-icon-upload2"></i>
              直接上传文件
            </el-button>
          </div>
        </div>

        <div class="form-item" v-if="selectedAgentType === 'knowledge' && showUploadSection">
          <div class="form-label">
            <span>上传文档</span>
          </div>
          <div class="document-upload">
            <div class="document-types">
              <div class="doc-type-label">上传文档</div>
              <div class="doc-type-options">
                <div
                  class="doc-type-item"
                  :class="{ active: currentFileType === 'noStructure' }"
                  @click="selectFileType('noStructure')"
                >
                  <div class="doc-type-icon">
                    <i class="el-icon-document"></i>
                  </div>
                  <div class="doc-type-content">
                    <div class="doc-type-title">无结构文档</div>
                    <div class="doc-type-desc">自动解析文档，使用方便</div>
                  </div>
                </div>
                <div
                  class="doc-type-item"
                  :class="{ active: currentFileType === 'qa' }"
                  @click="selectFileType('qa')"
                >
                  <div class="doc-type-icon">
                    <i class="el-icon-question"></i>
                  </div>
                  <div class="doc-type-content">
                    <div class="doc-type-title">QA问答格式</div>
                    <div class="doc-type-desc">一问一答导入，准确性更佳</div>
                  </div>
                </div>
              </div>
            </div>

            <div class="upload-area">
              <div class="upload-container">
                <el-upload
                  class="file-uploader"
                  style="width: 100%"
                  action="#"
                  :auto-upload="false"
                  :show-file-list="true"
                  :on-change="handleFileChange"
                  :on-remove="handleFileRemove"
                  :file-list="fileList"
                  :accept="currentFileAccept"
                  :before-upload="beforeFileUpload"
                multiple
                  drag
                >
                  <div class="upload-placeholder">
                    <div class="upload-icon">
                <i class="el-icon-upload"></i>
              </div>
                    <div class="upload-text">
                      拖入文件 或 <span class="upload-link">点击上传</span>
            </div>
          </div>
                </el-upload>
        </div>
              <div class="file-tips">
                {{ currentFileType === 'noStructure' ? '支持 txt, pdf, md, docx格式文件' : '支持 csv 文件' }}
                <span v-if="currentFileType === 'qa'" class="download-template" @click="downloadTemplate">
                  | <a href="javascript:void(0)">下载模板</a>
                </span>
              </div>
            </div>
          </div>
          <div class="kb-selector-link">
            <el-button
              type="text"
              class="switch-to-kb-btn"
              @click="toggleUploadSection"
            >
              <i class="el-icon-collection"></i>
              绑定已有知识库
            </el-button>
          </div>
        </div>

        <!-- 知识库选择对话框 -->
        <el-dialog
          title="添加知识库"
          :visible="knowledgeDialogVisible"
          width="80%"
          :close-on-click-modal="false"
          class="knowledge-dialog"
          :append-to-body="true"
          :z-index="3000"
        >
          <div class="dialog-content">
            <div class="dialog-header">
              <div class="search-section">
                <div class="search-box">
                  <img src="@/assets/app/search-icon.png" alt="搜索" class="search-icon" />
                  <el-input
                    v-model="searchKeyword"
                    placeholder="搜索知识库名称"
                    clearable
                    @input="handleSearchChange"
                    class="search-input"
                  ></el-input>
                </div>
                <el-button type="primary" class="add-kb-btn">
                  <img src="@/assets/app/add-agent-icon.png" alt="新增" class="btn-icon" />
                  新增知识库
                </el-button>
              </div>
            </div>

            <div class="knowledge-list">
              <div
                v-for="item in filteredKnowledgeList"
                :key="item.id"
                class="knowledge-item"
                :class="{ 'selected': selectedKnowledgeIds.includes(item.id) }"
                @click="toggleKnowledgeSelection(item.id)"
              >
                <div class="item-icon">
                  <img
                    :src="getKnowledgeIcon(item)"
                    alt="知识库"
                    class="kb-icon"
                  />
                </div>
                <div class="item-content">
                  <div class="item-title">{{ item.name }}</div>
                  <div class="item-description">
                    {{ item.description || "暂无描述" }}
                  </div>
                </div>
                <div class="selection-indicator">
                  <img
                    :src="selectedKnowledgeIds.includes(item.id) ? require('@/assets/app/radio-selected.png') : require('@/assets/app/radio-unselected.png')"
                    alt="选择状态"
                    class="radio-icon"
                  />
                </div>
              </div>
            </div>
          </div>

          <div slot="footer" class="dialog-footer">
            <div class="footer-buttons">
              <el-button @click="knowledgeDialogVisible = false">取消</el-button>
              <el-button type="primary" @click="handleKnowledgeConfirm">
                确定（{{ selectedKnowledgeIds.length }}）
              </el-button>
            </div>
          </div>
        </el-dialog>
      </div>

      <div class="dialog-footer">
        <div class="create-btn" @click="createAgent" :class="{ loading: loading }">
          <img src="@/assets/app/create-agent-btn.png" alt="创建" class="btn-icon" />
          <span v-if="!loading">创建智能体</span>
          <i v-else class="el-icon-loading"></i>
        </div>
      </div>
    </div>
  </el-dialog>
</template>

<script>
import { api } from "@/api/request"
import { getNodesByAppType } from "@/utils/appTemplates"
import { EnumApplicationType } from "@/utils/enums"

export default {
  name: "CreateAgentDialog",
  props: {
    visible: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      selectedAgentType: 'light', // 默认选中轻量智能体
      loading: false, // 加载状态
      agentForm: {
        avatar: '',
        name: '',
        description: '',
        introduction: '',
        type: 'light',
        setting: '',
        isPublic: false,
        applicationTypeCode: '',
        applicationTypeName: '',
        sessionFlowCode: '1',
        knowledgeBaseIds: ''
      },
      categories: EnumApplicationType,
      // 知识库相关数据
      knowledgeDialogVisible: false,
      selectedKnowledgeIds: [],
      selectedKnowledgeCodes: [],
      selectedKnowledgeList: [],
      knowledgeList: [],
      searchKeyword: "",
      // 文件上传相关
      fileList: [],
      currentFileType: "noStructure",
      showUploadSection: false
    };
  },
  mounted() {
    console.log('CreateAgentDialog mounted, selectedAgentType:', this.selectedAgentType);
  },
  computed: {
    // 过滤知识库列表
    filteredKnowledgeList() {
      if (!this.searchKeyword) {
        return this.knowledgeList;
      }
      const keyword = this.searchKeyword.toLowerCase();
      return this.knowledgeList.filter(item =>
        item.name.toLowerCase().includes(keyword) ||
        (item.description && item.description.toLowerCase().includes(keyword))
      );
    },
    // 当前文档类型支持的文件格式
    currentFileAccept() {
      if (this.currentFileType === 'qa') {
        return '.csv';
      } else {
        return '.txt,.pdf,.md,.docx';
      }
    }
  },
  watch: {
    selectedAgentType(newType) {
      this.agentForm.type = newType;
      this.agentForm.sessionFlowCode = newType === "light" ? "1" : "2";
      if (newType === 'knowledge') {
        this.fetchKnowledgeList();
      }
    }
  },
  methods: {
    // 选择智能体类型
    selectAgentType(type) {
      this.selectedAgentType = type;
      this.agentForm.type = type;
    },

    // 触发头像上传
    triggerAvatarUpload() {
      this.$refs.avatarInput.click();
    },

    // 处理头像上传
    async handleAvatarUpload(event) {
      const file = event.target.files[0];
      if (file) {
        // 检查文件大小（2MB限制）
        if (file.size > 2 * 1024 * 1024) {
          this.$message.error('文件大小不能超过2MB');
          return;
        }

        // 检查文件类型
        if (!['image/jpeg', 'image/png'].includes(file.type)) {
          this.$message.error('只支持jpg、png格式的图片');
          return;
        }

        try {
          // 构建上传参数
          const uploadParams = {
            file: file,
            fileType: "image",
            folder: "app_avatars",
          };

          const uploadRes = await api.oss.upload(uploadParams);
          if (uploadRes.data?.fileUrl) {
            this.agentForm.avatar = uploadRes.data.fileUrl;
            this.$message.success("上传成功");
          } else {
            throw new Error(uploadRes.message || "上传失败");
          }
        } catch (error) {
          console.error("上传头像失败:", error);
          this.$message.error("上传失败，请稍后重试");
        }
      }
    },

    // AI一键填写描述
    async aiFillDescription() {
      // 验证必填字段：智能体名称和智能体描述
      if (!this.agentForm.name.trim()) {
        this.$showFriendlyError(null, '请您填写智能体名称后才可使用此功能');
        return;
      }

      if (!this.agentForm.description.trim()) {
        this.$showFriendlyError(null, '请您填写智能体描述后才可使用此功能');
        return;
      }

      // 创建全页面加载状态
      const loadingInstance = this.$loading({
        lock: true,
        text: '正在帮您生成，请稍后...',
        spinner: 'el-icon-loading',
        background: 'rgba(0, 0, 0, 0.7)',
        customClass: 'ai-full-page-loading'
      });

      try {
        // 构建基础参数
        const baseParams = {
          agentName: this.agentForm.name.trim(),
          agentCategory: this.categories.find(cat => cat.value === this.agentForm.applicationTypeCode)?.label || "其他",
          model: "qwen-turbo"
        };

        // 并发调用两个接口
        const [introductionRes, settingsRes] = await Promise.all([
          // 调用开场介绍优化接口
          api.agentService.optimizeAgentDescription({
            ...baseParams,
            currentDescription: this.agentForm.description.trim(),
            require: `### **母版提示词：AI智能体开场白优化师**

              你是一位顶级的AI智能体对话体验优化专家，专注于打造亲切、清晰且能有效引导用户的对话开场白。

              #### **核心目标**
              你的任务是基于用户提供的 **[智能体名称]**、**[智能体描述]**、**[应用分类]** 和 **[当前智能体开场白]**，摒弃后者，并根据前三者信息，创作出一个全新的、高质量的开场介绍文案。

              #### **核心原则**
              生成的开场白必须遵循以下四个黄金原则：

              1.  **身份清晰 (Who I am):** 在开场白中明确介绍自己的身份或名字，建立认知。
              2.  **价值主张 (What I do for you):** 用一句话概括能为用户解决的核心问题或提供的核心价值，让用户秒懂你的用途。
              3.  **行动号召 (What you should do next):** 必须包含一个清晰、简单的指令或问题，引导用户开启对话，消除用户的输入疑虑。
              4.  **风格匹配 (How I sound):** 开场白的语气和风格必须与智能体的名称、描述和应用分类高度一致。（例如："法律顾问"应专业严谨，"旅游规划师"应热情活泼）。

              #### **工作流程**
              1.  **全面分析：** 仔细阅读并理解用户提供的\`[智能体名称]\`、\`[智能体描述]\`和\`[应用分类]\`，忽略\`[当前智能体开场白]\`的内容，只将其作为反面案例。
              2.  **确定风格：** 根据分析结果，确定最合适的对话风格。例如：
                  *   **办公效率类：** 专业、高效、直接。
                  *   **内容创作类：** 创意、活泼、鼓励。
                  *   **生活服务类：** 亲切、耐心、可靠。
                  *   **专业咨询类：** 严谨、权威、值得信赖。
              3.  **结构化构建：** 按照【问候语 + 自我介绍（身份）+ 功能说明（价值）+ 引导提问（行动）】的结构来组织语言。
              4.  **语言润色：** 对构建好的句子进行精炼，使其更加自然、流畅、友好。可以适当使用1-2个贴合角色的Emoji来增加亲和力，但避免过度使用。
              5.  **最终输出：** 直接呈现最终优化好的开场白文案。

              #### **输出要求**
              *   **直接交付：** 你的回答**必须且只能是**最终生成的那段开场白文本，不要包含任何"这是优化后的开场白："之类的额外解释。
              *   **简洁有力：** 文本长度建议控制在30-80字之间，确保信息密度高且易于阅读。
              *   **引导性强：** 必须以一个开放式问题或一个明确的指令结束。
              *   **完全原创：** 忽略用户提供的\`[当前智能体开场白]\`，基于前三个要素进行全新创作。`,
            text: ""
          }),
          // 调用智能体设定优化接口
          api.agentService.optimizeAgentDescription({
            ...baseParams,
            currentDescription: this.agentForm.description.trim(),
            require: `### **母版提示词：智能体提示词（Prompt）架构师**

              你是一位顶级的AI智能体提示词（Prompt）架构师和优化专家。

              你的核心任务是基于用户提供的 **[智能体描述]**、**[应用分类]** 和 **[当前提示词]**，生成一个结构化、规范化、高效且合规的全新提示词。新生成的提示词必须严格遵循【角色、目标、技能、工作流、注意、示例】这六个部分进行组织，以确保AI智能体能够稳定、高质量地执行任务。

              #### **核心技能**

              1.  **深度理解与分析：** 精准提炼用户输入信息中的核心需求、功能点和约束条件。
              2.  **结构化重构：** 将零散的需求和描述，重构成逻辑清晰、层次分明的结构化文本。
              3.  **Prompt工程专业知识：** 熟悉大型语言模型的工作原理，知道如何通过精确的指令（如角色扮演、思维链、提供示例等）来激发模型最佳性能。
              4.  **语言精炼：** 使用专业、精确、无歧义的语言，避免口语化和模糊不清的表达。
              5.  **创新性设计：** 在遵循规范的基础上，能够创造性地设计出清晰的工作流程和高质量的示例，使最终的提示词极具可用性。

              #### **工作流程**

              1.  **接收与解析：**
                  *   仔细阅读用户提供的三个输入：
                      *   \`[智能体描述]\`：这是关于智能体是什么、用来做什么的简要说明。
                      *   \`[应用分类]\`：这指明了智能体所属的领域，如"内容创作"、"代码生成"、"客户服务"等。
                      *   \`[当前提示词]\`：这是用户已有的、可能不规范或过于简单的原始提示词。

              2.  **提炼与映射：**
                  *   **角色 (Role):** 从 \`[智能体描述]\` 中提炼出智能体的核心身份和专业定位，用一句话清晰定义。例如，"一位资深的小红书爆款文案写手"。
                  *   **目标 (Goal):** 结合 \`[智能体描述]\` 和 \`[应用分类]\`，明确智能体需要完成的核心任务或解决的关键问题。
                  *   **技能 (Skills):** 分析 \`[当前提示词]\` 和 \`[智能体描述]\`，将智能体为完成目标所必须具备的具体能力，以列表形式分点阐述。
                  *   **工作流 (Workflow):** 这是最关键的一步。你需要根据目标和技能，设计一个清晰、可执行的步骤化流程。如果 \`[当前提示词]\` 中已有流程，请优化并使其更具逻辑性；如果 \`[当前提示词]\` 只是一个简单的命令，你需要为其创造一个完整的工作流。
                  *   **注意 (Attention):** 总结智能体在执行任务时必须遵守的规则、限制或风格要求。例如，输出格式、语言风格、禁止行为、信息来源等。
                  *   **示例 (Example):** 基于上述所有部分，创建一个或多个具体的"用户输入"和"智能体理想输出"的配对示例。这个示例应该能完美展示工作流的执行过程和最终产出质量，为AI提供一个清晰的模仿范本。

              3.  **整合与输出：**
                  *   将上述六个部分整合起来，形成一个完整、规范的提示词。
                  *   使用 \`##\` 作为每个部分的标题（例如 \`## 角色\`）。
                  *   直接输出这个全新的、结构化的提示词，无需任何额外的解释或开场白。

              #### **输出要求**

              *   **严格遵循格式：** 输出必须且只能包含【角色、目标、技能、工作流、注意、示例】六个部分，并使用指定的Markdown标题格式。
              *   **内容忠实于输入：** 生成的内容必须完全基于用户提供的三个输入进行优化和重构，不得凭空捏造核心功能。
              *   **专业与简洁：** 语言专业、书面化，内容简洁明了。
              *   **完整性：** 确保每个部分都内容详实，特别是"工作流"和"示例"部分，它们是提示词成功的关键。`,
            text: ""
          })
        ]);

        // 处理开场介绍结果
        if (introductionRes.isSuccess && introductionRes.data?.optimizedDescription) {
          this.agentForm.introduction = introductionRes.data.optimizedDescription;
        } else {
          console.warn('开场介绍生成失败:', introductionRes.message);
        }

        // 处理智能体设定结果
        if (settingsRes.isSuccess && settingsRes.data?.optimizedDescription) {
          this.agentForm.setting = settingsRes.data.optimizedDescription;
        } else {
          console.warn('智能体设定生成失败:', settingsRes.message);
        }

        // 检查是否有任何内容生成成功
        if ((introductionRes.isSuccess && introductionRes.data?.optimizedDescription) ||
            (settingsRes.isSuccess && settingsRes.data?.optimizedDescription)) {
          this.$message.success('AI内容生成成功！请检查并根据需要进行调整。');
        } else {
          this.$showFriendlyError(null, 'AI生成失败，请稍后重试');
        }

      } catch (error) {
        console.error('AI一键填写失败:', error);
        this.$showFriendlyError(error, 'AI生成失败，请稍后重试');
      } finally {
        // 关闭全页面加载状态
        loadingInstance.close();
      }
    },

    // 去模版中心复制
    goToTemplateCenter() {
      this.$message.info("为了给您更完美的体验，我们正在做最后的冲刺，很快解锁");
    },

    // 显示知识库选择对话框
    showKnowledgeDialog() {
      this.knowledgeDialogVisible = true;
      if (this.knowledgeList.length === 0) {
        this.fetchKnowledgeList();
      }
    },

    // 确认知识库选择
    handleKnowledgeConfirm() {
      this.selectedKnowledgeList = this.knowledgeList.filter((item) =>
        this.selectedKnowledgeIds.includes(item.id)
      );

      // 更新知识库codes
      this.selectedKnowledgeCodes = this.selectedKnowledgeList.map(
        (item) => item.code
      );
      console.log("选择的知识库Codes:", this.selectedKnowledgeCodes);

      this.knowledgeDialogVisible = false;
    },

    // 切换知识库选择
    toggleKnowledgeSelection(id) {
      if (this.selectedKnowledgeIds.includes(id)) {
        this.selectedKnowledgeIds = this.selectedKnowledgeIds.filter(
          (i) => i !== id
        );
      } else {
        this.selectedKnowledgeIds.push(id);
      }
    },

    // 移除知识库选择
    removeKnowledgeSelection(id) {
      this.selectedKnowledgeIds = this.selectedKnowledgeIds.filter(
        (i) => i !== id
      );

      // 更新知识库列表
      this.selectedKnowledgeList = this.selectedKnowledgeList.filter(
        (item) => item.id !== id
      );

      // 更新知识库codes
      this.selectedKnowledgeCodes = this.selectedKnowledgeList.map(
        (item) => item.code
      );
      console.log("移除后的知识库Codes:", this.selectedKnowledgeCodes);
    },

    // 切换上传区域显示
    toggleUploadSection() {
      this.showUploadSection = !this.showUploadSection;
    },

    // 处理分类变化
    handleCategoryChange(value) {
      const category = this.categories.find((item) => item.value === value);
      if (category) {
        this.agentForm.applicationTypeName = category.label;
        this.agentForm.applicationTypeCode = category.value;
      }
    },

    // 处理搜索变化
    handleSearchChange() {
      // 搜索输入变化时，直接通过computed重新计算filteredKnowledgeList
    },

    // 获取知识库图标
    getKnowledgeIcon(item) {
      // 根据知识库名称或类型返回不同的图标
      const name = item.name || '';
      if (name.includes('物流')) {
        return require('@/assets/app/knowledge-icon-logistics.png');
      } else if (name.includes('医药') || name.includes('电商')) {
        return require('@/assets/app/knowledge-icon-medical.png');
      } else {
        return require('@/assets/app/knowledge-icon-default.png');
      }
    },

    // 选择文件类型
    selectFileType(type) {
      this.currentFileType = type;
    },

    // 触发文件上传
    triggerFileUpload() {
      this.$refs.fileInput.click();
    },

    // 处理文件选择变化
    handleFileChange(file, fileList) {
      this.fileList = fileList.slice(0, 30); // 限制最多30个文件
    },
    // 处理文件移除
    handleFileRemove(file, fileList) {
      this.fileList = fileList;
    },
    // 下载QA模板
    downloadTemplate() {
      const templateUrl = 'https://yuanzhiqi-test.obs.cn-southwest-2.myhuaweicloud.com/fileDemo/qa_template.csv';
      const link = document.createElement('a');
      link.href = templateUrl;
      link.download = 'qa_template.csv';
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    },
    // 文件上传前验证
    beforeFileUpload(file) {
      // 根据当前文档类型获取允许的文件格式
      const allowedFormats = this.getAllowedFileFormats();

      // 检查文件扩展名
      const fileName = file.name.toLowerCase();
      const isValidType = allowedFormats.extensions.some(ext => fileName.endsWith(ext));

      // 检查MIME类型
      const isValidMime = allowedFormats.mimeTypes.includes(file.type);

      // 检查文件大小 (30MB)
      const isValidSize = file.size / 1024 / 1024 <= 30;

      if (!isValidType && !isValidMime) {
        this.$showFriendlyError(null, allowedFormats.errorMessage);
        return false;
      }

      if (!isValidSize) {
        this.$showFriendlyError(null, '文件大小不能超过30MB');
        return false;
      }

      return true;
    },

    // 获取当前文档类型允许的文件格式
    getAllowedFileFormats() {
      if (this.currentFileType === 'qa') {
        return {
          extensions: ['.csv'],
          mimeTypes: ['text/csv', 'application/csv'],
          errorMessage: '请上传CSV格式文件'
        };
      } else {
        return {
          extensions: ['.txt', '.pdf', '.md', '.docx'],
          mimeTypes: [
            'text/plain',
            'application/pdf',
            'text/markdown',
            'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
          ],
          errorMessage: '请上传txt, pdf, md, docx格式文件'
        };
      }
    },

    // 处理文件上传
    handleFileUpload(event) {
      const files = event.target.files;
      if (files.length > 0) {
        this.$message.success(`成功上传 ${files.length} 个文件`);
        console.log('上传的文件:', files);
      }
    },

    // 获取知识库列表
    async fetchKnowledgeList() {
      try {
        const params = {
          keyword: "",
          pageIndex: 0,
          pageSize: 100,
        };
        const res = await api.rag.getKnowledgeList(params);
        if (res.code === 200) {
          this.knowledgeList = res.data.items || [];
        } else {
          this.$message.error(res.message || "获取知识库列表失败");
        }
      } catch (error) {
        console.error("获取知识库列表失败:", error);
        this.$message.error("获取知识库列表失败");
      }
    },

    // 组装应用数据
    assembleAppData() {
      // 获取节点结构模板
      const flowDetailInput = getNodesByAppType(this.selectedAgentType);

      // 添加智能体名称到流程
      flowDetailInput.name = this.agentForm.name;

      // 如果是知识智能体且已选择知识库，更新知识库节点配置
      if (
        this.selectedAgentType === "knowledge" &&
        this.selectedKnowledgeCodes.length > 0
      ) {
        if (
          flowDetailInput.knowledgeNodes &&
          flowDetailInput.knowledgeNodes.length > 0
        ) {
          const kbNode = flowDetailInput.knowledgeNodes[0];
          // 将知识库code用英文逗号隔开拼接
          kbNode.data.nodeTypeConfig.knowledgeBase =
            this.selectedKnowledgeCodes.join(",");
        }
      }

      // 设置大模型节点的系统提示词
      if (
        flowDetailInput.largeModelNodes &&
        flowDetailInput.largeModelNodes.length > 0
      ) {
        const modelNode = flowDetailInput.largeModelNodes[0];
        // 更新系统提示词为表单中的智能体设定
        modelNode.data.nodeTypeConfig.systemPrompt =
          this.agentForm.setting;
      }
      flowDetailInput.humanTransferNodes = [];
      flowDetailInput.edges = [];

      // 构建完整请求数据
      const requestData = {
        name: this.agentForm.name,
        description: this.agentForm.description,
        introduce: this.agentForm.introduction || "有什么可以帮您?",
        profilePhoto: this.agentForm.avatar,
        isPublic: this.agentForm.isPublic,
        isTrueCustomerServer: this.agentForm.isTrueCustomerServer || false,
        applicationType: this.agentForm.applicationTypeCode,
        sessionFlowCode: this.agentForm.sessionFlowCode,
        SessionFlowSetting: this.agentForm.setting,
        createFlowDetailInput: flowDetailInput,
      };

      return requestData;
    },

    // 创建智能体
    async createAgent() {
      console.log("提交前检查智能体类型:", this.selectedAgentType);
      console.log("提交前检查上传区域显示:", this.showUploadSection);
      console.log("提交前检查知识库IDs:", this.selectedKnowledgeIds);
      console.log("提交前检查知识库Codes:", this.selectedKnowledgeCodes);

      // 如果是知识智能体且选择了知识库绑定模式，把知识库code设置到表单中
      if (this.selectedAgentType === "knowledge" && !this.showUploadSection) {
        // 使用知识库code而不是id
        this.agentForm.knowledgeBaseIds = this.selectedKnowledgeCodes.join(",");
        console.log(
          "设置knowledgeBaseIds(codes)到formData:",
          this.agentForm.knowledgeBaseIds
        );
      } else if (this.selectedAgentType !== "knowledge" || this.showUploadSection) {
        // 非知识智能体或上传模式时，设置为空字符串
        this.agentForm.knowledgeBaseIds = "";
        console.log("非知识智能体或上传模式，设置空knowledgeBaseIds");
      }

      console.log("表单提交前的数据:", JSON.stringify(this.agentForm));

      // 表单验证
      if (!this.agentForm.avatar) {
        this.$message.error('请上传智能体头像');
        return;
      }
      if (!this.agentForm.name.trim()) {
        this.$message.error('请输入智能体名称');
        return;
      }
      if (!this.agentForm.description.trim()) {
        this.$message.error('请输入智能体描述');
        return;
      }
      if (!this.agentForm.introduction.trim()) {
        this.$message.error('请输入开场介绍');
        return;
      }
      if (!this.agentForm.applicationTypeCode) {
        this.$message.error('请选择智能体分类');
        return;
      }

      if (this.loading) {
        return;
      }

      this.loading = true;

      try {
        this.$message.info('正在创建智能体...');

        const requestData = this.assembleAppData();
        console.log('创建智能体请求数据:', JSON.stringify(requestData));

        const res = await api.sessionFlow.create(requestData);
        if (res.code === 200) {
          this.$message.success('智能体创建成功');
          this.handleClose();
          this.resetForm();
          this.$emit('success');
        } else {
          throw new Error(res.message || '创建失败');
        }
      } catch (error) {
        console.error('创建智能体失败:', error);
        this.$message.error('创建智能体失败，请重试');
      } finally {
        this.loading = false;
      }
    },

    // 重置表单
    resetForm() {
      this.selectedAgentType = 'light';
      this.agentForm = {
        avatar: '',
        name: '',
        description: '',
        introduction: '',
        type: 'light',
        setting: '',
        isPublic: false,
        applicationTypeCode: '',
        applicationTypeName: '',
        sessionFlowCode: '1',
        knowledgeBaseIds: ''
      };
      // 重置知识库相关数据
      this.selectedKnowledgeIds = [];
      this.selectedKnowledgeCodes = [];
      this.selectedKnowledgeList = [];
      this.searchKeyword = "";
      this.showUploadSection = false;
    },

    // 关闭弹窗
    handleClose() {
      this.$emit('update:visible', false);
      this.resetForm();
    }
  }
};
</script>

<style lang="scss" scoped>
// 创建智能体弹窗样式
:deep(.create-agent-dialog) {
  z-index: 2000 !important;

  :deep(.el-dialog) {
    z-index: 2000 !important;
    max-height: 90vh;
    display: flex;
    flex-direction: column;
  }

  :deep(.el-dialog__wrapper) {
    z-index: 2000 !important;
  }

  :deep(.el-dialog__body) {
    padding: 0;
    flex: 1;
    overflow: hidden;
  }

  .el-dialog__header {
    display: none;
  }

  .el-dialog__body {
    padding: 0;
  }
}

.create-agent-dialog-content {
  background-color: #fff;
  border-radius: 8px;
  width: 100%;
  height: 100%;
  position: relative;
  display: flex;
  flex-direction: column;

  .dialog-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 26px 24px 0 24px;
    height: 26px;

    .dialog-title {
      color: #000;
      font-size: 18px;
      font-family: PingFangSC-Medium;
      font-weight: 500;
      line-height: 26px;
    }

    .close-icon {
      width: 12px;
      height: 12px;
      cursor: pointer;
    }
  }

  .agent-type-selection {
    display: flex;
    gap: 20px;
    padding: 24px 40px;
    margin: 0 24px;

    .agent-type-card {
      width: 400px;
      height: 74px;
      border-radius: 8px;
      cursor: pointer;
      transition: all 0.3s ease;
      display: flex;
      flex-direction: column;
      justify-content: center;
      position: relative;

      &.light {
        background-color: #f4f6f8;
        border: 0.5px solid transparent;

        &.active {
          background-color: #f0f9ff;
        }
      }

      &.knowledge {
        background-color: #f4f9ff;
        border: 0.5px solid transparent;

        &.active {
          background-color: #f0f9ff;
        }
      }

      &.active {
        border: 0.5px solid #256dff;
      }

      .agent-type-header {
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 8px;
        margin-bottom: 4px;

        .agent-type-icon {
          width: 16px;
          height: 18px;
        }

        .agent-type-name {
          font-size: 16px;
          font-family: PingFangSC-Medium;
          font-weight: 500;
          line-height: 22px;
          color: #000;
        }
      }

      .agent-type-desc {
        font-size: 12px;
        color: #999;
        line-height: 18px;
        text-align: center;
        margin: 0 auto;
        width: 168px;
      }

      &.knowledge.active .agent-type-name {
        color: #256dff;
      }
    }
  }

  .form-section {
    padding: 0 24px;
    flex: 1;
    overflow-y: auto;
    padding-bottom: 120px; // 为底部按钮留出足够空间
    max-height: calc(100vh - 200px); // 限制最大高度

    .form-item {
      margin-bottom: 20px;

      .form-label {
        display: flex;
        align-items: center;
        margin-bottom: 8px;
        font-size: 14px;
        color: #000;
        font-weight: 500;

        .required {
          color: #ff0000;
          margin-left: 4px;
        }
      }

      .avatar-upload {
        .avatar-preview {
          width: 80px;
          height: 80px;
          border-radius: 8px;
          border: 2px dashed #d9d9d9;
          display: flex;
          align-items: center;
          justify-content: center;
          margin-bottom: 8px;
          cursor: pointer;
          transition: all 0.3s;

          &:hover {
            border-color: #256dff;
          }

          .avatar-img {
            width: 100%;
            height: 100%;
            object-fit: cover;
            border-radius: 6px;
          }

          .avatar-placeholder {
            color: #999;
            font-size: 24px;
          }
        }

        .upload-tip {
          font-size: 12px;
          color: #999;
        }
      }

      .description-header {
        display: flex;
        gap: 16px;
        margin-bottom: 8px;

        span {
          font-size: 14px;
          color: #256dff;
          cursor: pointer;

          &:hover {
            text-decoration: underline;
          }
        }
      }

      .intro-tip {
        font-size: 12px;
        color: #666;
        margin-bottom: 8px;
        line-height: 16px;
      }

      .setting-tip {
        font-size: 12px;
        color: #666;
        margin-bottom: 8px;
      }

      .public-switch {
        display: flex;
        align-items: center;
        gap: 8px;

        .public-tip {
          font-size: 12px;
          color: #666;
        }
      }

      .knowledge-section {
        .knowledge-actions {
          display: flex;
          gap: 12px;
          margin-bottom: 8px;

          .knowledge-action {
            display: flex;
            align-items: center;
            gap: 4px;
            padding: 8px 12px;
            background-color: #f8f9fa;
            border-radius: 4px;
            cursor: pointer;
            transition: all 0.3s;

            &:hover {
              background-color: #f0f2f5;
            }

            .action-icon {
              width: 12px;
              height: 12px;
            }

            span {
              font-size: 12px;
              color: #666;
            }
          }
        }

        .upload-tip {
          font-size: 12px;
          color: #999;
        }

        .expand-section {
          display: flex;
          justify-content: flex-end;
          margin-top: 8px;

          .expand-icon {
            width: 8px;
            height: 8px;
            cursor: pointer;
            opacity: 0.6;

            &:hover {
              opacity: 1;
            }
          }
        }

        .file-upload-section {
          margin-top: 12px;

          .upload-area {
            border: 2px dashed #d9d9d9;
            border-radius: 4px;
            padding: 20px;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s;

            &:hover {
              border-color: #256dff;
              background-color: #f8f9fa;
            }

            i {
              font-size: 24px;
              color: #999;
              margin-bottom: 8px;
              display: block;
            }

            span {
              font-size: 14px;
              color: #666;
            }
          }
        }
      }
    }

    .form-input,
    .form-textarea,
    .form-select {
      width: 100%;

      :deep(.el-input__inner) {
        border-radius: 4px;
        border: 1px solid #d9d9d9;

        &:focus {
          border-color: #256dff;
        }
      }

      :deep(.el-textarea__inner) {
        border-radius: 4px;
        border: 1px solid #d9d9d9;

        &:focus {
          border-color: #256dff;
        }
      }
    }
  }

  .dialog-footer {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    padding: 24px;
    border-top: 1px solid #f0f0f0;
    display: flex;
    justify-content: center;
    background-color: #fff;
    z-index: 10;
    min-height: 80px;

    .create-btn {
      background-color: #256dff;
      border-radius: 4px;
      height: 40px;
      padding: 0 24px;
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      transition: background-color 0.3s;
      gap: 8px;

      &:hover {
        background-color: #1e5ce6;
      }

      &.loading {
        cursor: not-allowed;
        opacity: 0.8;
      }

      .btn-icon {
        width: 16px;
        height: 16px;
      }

      span {
        color: #fff;
        font-size: 14px;
        font-weight: 500;
      }

      i {
        color: #fff;
        font-size: 16px;
        animation: rotate 1s linear infinite;
      }
    }
  }

  // 知识库选择相关样式
  .knowledge-base-selection {
    display: flex;
    align-items: center;
    gap: 16px;

    .selected-knowledge {
      flex: 1;
      padding: 8px 12px;
      border: 1px solid #dcdfe6;
      border-radius: 4px;
      min-height: 40px;
      display: flex;
      align-items: center;
      color: #606266;

      .knowledge-tags {
        display: flex;
        flex-wrap: wrap;
        gap: 8px;
        width: 100%;

        .knowledge-tag {
          margin-right: 0;
        }
      }
    }
  }

  .form-tip {
    margin-top: 4px;
    font-size: 12px;
    color: #909399;
  }

  .direct-upload {
    margin-top: 12px;

    .direct-upload-btn {
      padding: 0;
      height: auto;
      font-size: 14px;
      color: #409eff;

      i {
        margin-right: 4px;
      }
    }
  }

  .kb-selector-link {
    margin-top: 12px;

    .switch-to-kb-btn {
      padding: 0;
      height: auto;
      font-size: 14px;
      color: #409eff;

      i {
        margin-right: 4px;
      }
    }
  }

  // 文档上传相关样式
  .document-upload {
    margin-top: 10px;
    border: 1px solid #ebeef5;
    border-radius: 4px;
    padding: 20px;
    background-color: #f9f9f9;

    .document-types {
      display: flex;
      flex-direction: column;
      margin-bottom: 20px;

      .doc-type-label {
        font-size: 14px;
        font-weight: 500;
        margin-bottom: 10px;
      }

      .doc-type-options {
        display: flex;
        gap: 20px;

        .doc-type-item {
          flex: 1;
          padding: 15px;
          border: 1px solid #dcdfe6;
          border-radius: 4px;
          cursor: pointer;
          background-color: #fff;
          display: flex;
          align-items: center;
          gap: 10px;
          transition: all 0.3s;

          &:hover {
            border-color: var(--el-color-primary);
            box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
          }

          &.active {
            border-color: var(--el-color-primary);
            background-color: var(--el-color-primary-light-9);
          }

          .doc-type-icon {
            i {
              font-size: 24px;
              color: var(--el-color-primary);
            }
          }

          .doc-type-content {
            .doc-type-title {
              font-size: 16px;
              font-weight: 500;
              margin-bottom: 5px;
            }

            .doc-type-desc {
              font-size: 12px;
              color: #909399;
            }
          }
        }
      }
    }

    .upload-area {
      border: 1px dashed #d9d9d9;
      border-radius: 6px;
      background-color: #fafafa;
      margin-bottom: 10px;

      .upload-container {
        .file-uploader {
          width: 100%;

          .upload-placeholder {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            height: 150px;
            cursor: pointer;
            position: relative;
            transition: border-color 0.3s;

            .upload-icon {
              i {
                font-size: 48px;
                color: #c0c4cc;
                margin-bottom: 10px;
              }
            }

            .upload-text {
              font-size: 14px;
              color: #606266;
              text-align: center;

              .upload-link {
                color: var(--el-color-primary);
                font-style: normal;
                text-decoration: underline;
              }
            }
          }
        }
      }

      .file-tips {
        padding: 10px 20px;
        font-size: 12px;
        color: #909399;
        line-height: 1.5;
        border-top: 1px dashed #d9d9d9;
        background-color: #fff;
        border-bottom-left-radius: 6px;
        border-bottom-right-radius: 6px;

        .download-template {
          a {
            color: #409eff;
            text-decoration: none;
            cursor: pointer;

            &:hover {
              text-decoration: underline;
            }
          }
        }
      }
    }
  }

    // 知识库选择对话框样式
  .knowledge-dialog {
    z-index: 3000 !important;

    :deep(.el-dialog) {
      z-index: 3000 !important;
    }

    :deep(.el-dialog__wrapper) {
      z-index: 3000 !important;
    }

    .dialog-content {
      display: flex;
      flex-direction: column;
      gap: 20px;
      min-height: 400px;
    }

    .dialog-header {
      .search-section {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-bottom: 20px;

        .search-box {
          display: flex;
          align-items: center;
          flex: 1;
          max-width: 400px;
          background-color: #f4f6f8;
          border-radius: 8px;
          padding: 0 12px;
          gap: 8px;

          .search-icon {
            width: 16px;
            height: 16px;
            flex-shrink: 0;
          }

          .search-input {
            flex: 1;

            :deep(.el-input__inner) {
              height: 40px;
              background: transparent;
              border: none;
              padding-left: 0;
              font-size: 14px;

              &::placeholder {
                color: #999;
              }
            }
          }
        }

        .add-kb-btn {
          display: flex;
          align-items: center;
          gap: 8px;
          height: 40px;
          padding: 0 16px;
          border-radius: 8px;
          background-color: #256dff;
          border: none;
          color: #fff;
          font-size: 14px;
          cursor: pointer;
          transition: all 0.3s;
          white-space: nowrap;

          &:hover {
            background-color: #1e5ce6;
          }

          .btn-icon {
            width: 16px;
            height: 16px;
            object-fit: contain;
          }
        }
      }
    }

    .knowledge-list {
      display: flex;
      flex-direction: column;
      gap: 12px;
      max-height: 450px;
      overflow-y: auto;
      padding-right: 8px;
    }

    .knowledge-item {
      border: 1px solid #e4e7ed;
      border-radius: 8px;
      padding: 16px;
      cursor: pointer;
      display: flex;
      align-items: center;
      gap: 16px;
      background-color: #fff;
      transition: all 0.3s ease;
      min-height: 72px;

      &:hover {
        border-color: #409eff;
        box-shadow: 0 2px 8px rgba(64, 158, 255, 0.1);
      }

      &.selected {
        border-color: #409eff;
        background-color: #f0f9ff;
      }

      .item-icon {
        width: 40px;
        height: 40px;
        border-radius: 8px;
        background-color: #f0f9ff;
        display: flex;
        align-items: center;
        justify-content: center;
        flex-shrink: 0;

        .kb-icon {
          width: 24px;
          height: 24px;
          object-fit: contain;
        }
      }

      .item-content {
        flex: 1;
        min-width: 0;

        .item-title {
          font-size: 16px;
          font-weight: 500;
          color: #303133;
          line-height: 1.4;
          margin-bottom: 4px;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }

        .item-description {
          font-size: 14px;
          color: #909399;
          line-height: 1.4;
          overflow: hidden;
          text-overflow: ellipsis;
          display: -webkit-box;
          -webkit-line-clamp: 2;
          -webkit-box-orient: vertical;
        }
      }

      .selection-indicator {
        flex-shrink: 0;

        .radio-icon {
          width: 20px;
          height: 20px;
          object-fit: contain;
        }
      }
    }

    :deep(.el-dialog__header) {
      padding: 20px 20px 0;
      border-bottom: 1px solid #ebeef5;
      margin-bottom: 0;

      .el-dialog__title {
        font-size: 18px;
        font-weight: 500;
        color: #303133;
      }
    }

    :deep(.el-dialog__body) {
      padding: 20px;
    }

    :deep(.el-dialog__footer) {
      padding: 15px 20px 20px;
      border-top: 1px solid #ebeef5;

      .dialog-footer {
        display: flex;
        align-items: center;
        justify-content: space-between;
        width: 100%;
      }

      .footer-buttons {
        display: flex;
        gap: 12px;
      }

      .el-button {
        padding: 8px 20px;
        font-size: 14px;
      }
    }
  }
}

@keyframes rotate {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

// AI全页面加载样式
:deep(.ai-full-page-loading) {
  .el-loading-mask {
    background-color: rgba(0, 0, 0, 0.8) !important;
  }

  .el-loading-spinner {
    .el-icon-loading {
      font-size: 48px !important;
      color: #409eff !important;
      animation: ai-rotate 2s linear infinite;
    }

    .el-loading-text {
      font-size: 16px !important;
      color: #ffffff !important;
      margin-top: 16px;
      font-weight: 500;
      letter-spacing: 1px;
    }
  }
}

@keyframes ai-rotate {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

// 全局样式确保知识库对话框正确显示
:deep(.knowledge-dialog) {
  .el-dialog {
    z-index: 3000 !important;
  }

  .el-dialog__wrapper {
    z-index: 3000 !important;
  }

  .v-modal {
    z-index: 2999 !important;
  }
}
</style>
